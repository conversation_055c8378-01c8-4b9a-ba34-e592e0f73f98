http:
  middlewares:
    jwt-auth:
      plugin:
        jwt:
          # This is the endpoint that will be used to validate the JWT token.
          issuers:
            - https://jkkxvdaponymlwtmzdrn.supabase.co/auth/v1

          # Allow token from query parameter for WebSocket connections
          parameterName: token
          
          # This specifies the claims that are required to be present in the JWT token.
          require:
            aud: authenticated

          # This maps specific claims from the JWT payload into request headers 
          # that will be forwarded to your backend service.
          headerMap:
            X-User-Id: "sub"
            X-User-Email: "email"
            X-Token-Audience: "aud"
            X-Token-Issuer: "iss"
            X-Token-Expires-At: "exp"
            X-Token-Issued-At: "iat"