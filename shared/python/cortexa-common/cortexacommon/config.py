from typing import Dict, Any
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class DatabaseSettings(BaseSettings):
    """Database configuration settings."""
    
    model_config = SettingsConfigDict(
        env_prefix="DB_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )
    
    host: str = Field(default="localhost", description="Database host")
    port: int = Field(default=5432, description="Database port")
    user: str = Field(default="cortexa_user", description="Database user")
    password: str = Field(default="cortexa_password", description="Database password")
    name: str = Field(default="cortexa", description="Database name")
    
    @property
    def url(self) -> str:
        """Get the database URL."""
        return f"postgresql+psycopg://{self.user}:{self.password}@{self.host}:{self.port}/{self.name}"


class KafkaSettings(BaseSettings):
    """Kafka configuration settings."""
    
    model_config = SettingsConfigDict(
        env_prefix="KAFKA_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )
    
    bootstrap_servers: str = Field(default="localhost:9092", description="Kafka bootstrap servers")
    group_id: str = Field(default="cortexa-group", description="Kafka consumer group ID")


class LoggingSettings(BaseSettings):
    """Logging configuration settings."""

    model_config = SettingsConfigDict(
        env_prefix="LOG_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )

    level: str = Field(
        default="INFO",
        description="Log level"
    )
    file: str | None = Field(
        default=None,
        description="Log file path"
    )
    max_file_size: str = Field(
        default="100 MB",
        description="Maximum log file size"
    )
    retention: str = Field(
        default="30 days",
        description="Log file retention period"
    )


class MonitoringSettings(BaseSettings):
    """Monitoring configuration settings."""

    model_config = SettingsConfigDict(
        env_prefix="MONITORING_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )

    metrics_enabled: bool = Field(
        default=True,
        description="Enable metrics collection"
    )
    tracing_enabled: bool = Field(
        default=True,
        description="Enable distributed tracing"
    )


class BaseServiceSettings(BaseSettings):
    """Base settings for all microservices."""

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )

    service_name: str = Field(description="Name of the microservice")
    debug: bool = Field(default=False, description="Debug mode")
    host: str = Field(default="0.0.0.0", description="Service host")
    port: int = Field(default=8000, description="Service port")
    environment: str = Field(default="development", description="Environment (development, staging, production)")

    # Shared settings
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    kafka: KafkaSettings = Field(default_factory=KafkaSettings)
    logging: LoggingSettings = Field(default_factory=LoggingSettings)
    monitoring: MonitoringSettings = Field(default_factory=MonitoringSettings)

    @property
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.environment.lower() == "production"

    def get_service_url(self) -> str:
        """Get the service URL."""
        protocol = "https" if self.is_production else "http"
        return f"{protocol}://{self.host}:{self.port}"

    def to_dict(self) -> Dict[str, Any]:
        """Convert settings to dictionary."""
        return self.model_dump()
