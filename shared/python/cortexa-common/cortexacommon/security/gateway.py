"""
Gateway Authentication Module (Adapted for Traefik and JWKS Plugin)

This module handles authentication by parsing individual claims forwarded
as headers by the agilezebra/jwt-middleware Traefik plugin.
"""

import logging
from typing import Any
from datetime import datetime, timezone
from dataclasses import dataclass

logger = logging.getLogger(__name__)


class GatewayAuthenticationError(Exception):
    """Exception raised for authentication errors."""
    pass


@dataclass
class AuthenticatedUser:
    """
    Represents an authenticated user with basic identity information.
    This data is extracted from headers provided by the Traefik gateway
    after successful JWT validation against a JWKS endpoint.
    """
    # Core identity
    user_id: str
    email: str

    # Token information
    token_audience: str
    token_issuer: str
    token_expires_at: int
    token_issued_at: int

    # Validation timestamp
    validated_at: str

    @property
    def is_token_expired(self) -> bool:
        """Check if the JWT token has expired."""
        current_timestamp = int(datetime.now(timezone.utc).timestamp())
        return current_timestamp >= self.token_expires_at


class GatewayAuthenticator:
    """
    Parses authentication information from individual gateway headers.
    """
    
    def __init__(self):
        """Initialize the gateway auth parser."""
        self.logger = logging.getLogger(f"{__name__}.GatewayAuthParser")
    
    def parse_user_from_headers(self, headers: dict[str, str]) -> AuthenticatedUser:
        """Parse authenticated user information from individual gateway headers."""
        user_id = headers.get("x-user-id") # Headers are case-insensitive
        if not user_id:
            self.logger.debug("No X-User-Id header found - request not authenticated")
            raise GatewayAuthenticationError("Request not authenticated")
        
        try:
            user = AuthenticatedUser(
                user_id=user_id,
                email=headers.get("x-user-email", ""),
                token_audience=headers.get("x-token-audience", ""),
                token_issuer=headers.get("x-token-issuer", ""),
                # JWT 'exp' and 'iat' are numeric timestamps, so we cast them
                token_expires_at=int(headers.get("x-token-expires-at", 0)),
                token_issued_at=int(headers.get("x-token-issued-at", 0)),
                validated_at=datetime.now(timezone.utc).isoformat()
            )
        except (ValueError, TypeError) as e:
            self.logger.error(f"Failed to parse token timestamp headers: {e}")
            raise GatewayAuthenticationError("Invalid token timestamp format in headers")

        if user.is_token_expired:
            self.logger.warning(f"Token expired for user {user.user_id}")
            raise GatewayAuthenticationError("Token has expired")
        
        self.logger.info(f"Successfully parsed user context for {user.user_id}")
        return user