[tool.poetry]
name = "cortexa-common"
version = "0.1.0"
description = "Common utilities and shared code for Cortexa microservices"
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"
packages = [{include = "cortexacommon"}]

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.116.1"
uvicorn = {extras = ["standard"], version = "^0.35.0"}
pydantic = {extras = ["email"], version = "^2.11.7"}
pydantic-settings = "^2.10.1"
sqlalchemy = {extras = ["asyncio"], version = "^2.0.23"}
alembic = "^1.13.0"
psycopg = {extras = ["binary"], version = "^3.1.0"}
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
python-multipart = "^0.0.6"
aiokafka = "^0.9.0"
prometheus-client = "^0.22.1"
loguru = "^0.7.2"

# OpenTelemetry tracing
opentelemetry-api = "^1.21.0"
opentelemetry-sdk = "^1.21.0"
opentelemetry-exporter-jaeger-thrift = "^1.21.0"
opentelemetry-exporter-otlp = "^1.21.0"
opentelemetry-instrumentation-fastapi = "^0.42b0"
opentelemetry-instrumentation-httpx = "^0.42b0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"