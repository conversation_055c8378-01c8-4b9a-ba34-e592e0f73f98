from typing import Any

from pydantic import BaseModel, Field


class WebSocketMessage(BaseModel):
    """Base class for WebSocket messages."""

    type: str = Field(description="Message type")
    call_id: str = Field(description="Unique call identifier")


class ConnectionEstablishedMessage(WebSocketMessage):
    """Message sent when WebSocket connection is established."""

    type: str = Field(default="connection_established", description="Message type")
    message: str = Field(description="Connection confirmation message")
    timestamp: float = Field(description="Timestamp of connection")


class CallEndedMessage(WebSocketMessage):
    """Message sent when call is ended."""

    type: str = Field(default="call_ended", description="Message type")
    message: str = Field(description="Call ended message")
    stats: dict[str, Any] = Field(description="Call statistics")
