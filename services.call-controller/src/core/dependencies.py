"""
FastAPI dependency injection functions for the call-controller service.

This module provides dependency injection functions that can be used
with FastAPI's Depends() to inject shared resources into endpoints.
"""


from .context import get_app_context, ApplicationContext


async def get_app_context_dependency() -> ApplicationContext:
    """
    FastAPI dependency to get the application context.
    
    Returns:
        ApplicationContext: The application context instance
    """
    return await get_app_context()
