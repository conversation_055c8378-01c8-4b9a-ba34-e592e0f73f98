from pydantic import Field
from pydantic_settings import SettingsConfigDict

from cortexacommon.config import BaseServiceSettings


class CallControllerSettings(BaseServiceSettings):
    """Call Controller service-specific settings."""

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )

    # Service identification
    service_name: str = Field(default="call-controller", description="Service name")
    port: int = Field(default=8002, description="Service port")


# Global settings instance
settings = CallControllerSettings()
