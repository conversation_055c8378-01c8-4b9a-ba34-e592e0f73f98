# from fastapi import APIRouter

# from .endpoints import call, health

# api_router = APIRouter()

# api_router.include_router(call.router, tags=["calls"])
# api_router.include_router(health.router, tags=["health"])



# POST /api/calls
# GET /api/calls
# POST /api/calls/{call_id}/accept
# POST /api/calls/{call_id}/hold
# POST /api/calls/{call_id}/mute
# POST /api/calls/{call_id}/unmute
# POST /api/calls/{call_id}/end

# GET /api/queue/status

# POST /api/operators/{operator_id}/status  e.g. AVAILABLE, BUSY or OFFLINE

# POST /api/calls/{call_id}/features/translation   To Enable Translation for a call
# DELETE /api/calls/{call_id}/features/translation   TO Disable Translation for a call
