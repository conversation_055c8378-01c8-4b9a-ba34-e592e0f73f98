# Cortexa Call Controller Service

This service manages the state of all calls and coordinates the various services involved in a call. It exposes a REST API for call lifecycle management and an internal API for service orchestration.

## REST Endpoints

### POST /api/calls

### GET /api/calls

### POST /api/calls/{call_id}/accept

### POST /api/calls/{call_id}/hold

### POST /api/calls/{call_id}/mute

### POST /api/calls/{call_id}/unmute

### POST /api/calls/{call_id}/end

### GET /api/queue/status

### POST /api/operators/{operator_id}/status
e.g. AVAILABLE, BUSY or OFFLINE

### POST /api/calls/{call_id}/features/translation
To enable translation for a call

### DELETE /api/calls/{call_id}/features/translation
To disable translation for a call
