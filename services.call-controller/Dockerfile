# Multi-stage Dockerfile for Call Controller Service

# 1. Builder Stage
FROM python:3.11-slim AS builder

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies for building
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Install Poetry
RUN pip install poetry

# Set work directory
WORKDIR /app

# Copy Poetry files
COPY services.call-controller/pyproject.toml services.call-controller/poetry.lock* ./

# Copy cortexa-common package
COPY shared/python/cortexa-common /shared/python/cortexa-common

# Configure Poetry and install dependencies
RUN poetry config virtualenvs.create false && \
    poetry install --only=main --no-root && \
    rm -rf ~/.cache/pypoetry

# 2. Final Stage
FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PATH="/home/<USER>/.local/bin:$PATH" \
    PYTHONPATH="/shared/python/cortexa-common:$PYTHONPATH"

# Install runtime system dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    libsndfile1 \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create a non-root user
RUN getent group app || groupadd -r app && \
    id -u app >/dev/null 2>&1 || useradd -r -g app -d /home/<USER>

# Set work directory
WORKDIR /home/<USER>

# Copy Python packages from builder stage
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# Ensure editable path dependency is available in final image
COPY shared/python/cortexa-common /shared/python/cortexa-common

# Copy application code
COPY --chown=app:app services.call-controller/src ./src


# Switch to non-root user
USER app

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8002/health || exit 1

# Expose port
EXPOSE 8002

# Run the application
CMD ["uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8002"]
