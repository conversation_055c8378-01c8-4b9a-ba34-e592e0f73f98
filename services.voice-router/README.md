# Cortexa Voice Router Service

This service provides a WebRTC SFU (Selective Forwarding Unit) for voice communication using mediasoup.

## REST Endpoints

### POST /internal/calls

Upon receiving the POST request, the Voice Router performs the necessary MediaSoup setup.
1. It gets a Worker from its pool of C++ subprocesses (or creates one if needed) using `mediasoup.createWorker()`.   
2. Within that worker, it creates a new MediaSoup Router for this specific call using `worker.createRouter()`.
3. A Router is the logical entity that will handle all media for this single call.  
4. The newly created Router object has a unique, read-only id property. This ID becomes the `mediaSessionId.`
5. The Voice Router stores the new Router object in its state, mapping it to this mediaSessionId (locally for now - Redis in future).

### POST /internal/calls/{session_id}/forks
Instructs the Voice Router to start forking audio from a specific session to an external RTP endpoint, such as an AI service for transcription or translation.

1. The request body must contain the destination details, including the target IP address, port, and supported RTP capabilities.
2. The service locates the active MediaSoup Router associated with the `{session_id}`.
3. It creates a PlainTransport on the router, which is designed for sending and receiving plain RTP traffic with non-WebRTC endpoints.   
4. For each existing audio Producer in the session (e.g., from the caller and the operator), the service creates a corresponding Consumer on the new PlainTransport.  
5. This action begins the real-time forwarding of audio RTP packets from the call to the specified external service.
6. A unique fork_id is generated for this specific fork, and it is returned in the response body. The Call Control Service should store this ID to manage the fork's lifecycle.

### DELETE /internal/calls/{session_id}/forks/{fork_id}
Stops a specific audio fork, halting the stream of RTP packets to the external service.

1. The service uses the {session_id} and {fork_id} to locate the specific Consumer and PlainTransport associated with the audio fork.
2. It calls the `.close()` method on the Consumer(s) and the PlainTransport. This immediately stops the media flow and releases the underlying server resources.   
3. The fork is removed from the session's state. The endpoint returns a 204 No Content on success.

### DELETE /internal/calls/{session_id}
Terminates an entire media session and cleans up all associated resources. This is called by the Call Control Service when a call has ended.

1. The service locates the MediaSoup Router instance corresponding to the {session_id}.
2. It calls the `.close()` method on the Router object. This is a cascading action that automatically closes and cleans up every   
3. Transport, Producer, and Consumer associated with that router.
4. The session is removed from the Voice Router's state. The endpoint returns a 204 No Content on success.


## WS Events

The WebSocket API provides the real-time, bidirectional signaling channel required for a WebRTC client (e.g., an operator's browser) to negotiate a media connection with the Voice Router. The following events describe the message-based protocol used over this connection.


| Event Name | Direction | Payload | Description |
|---|---|---|---|
| `getRouterRtpCapabilities` | Client → Server | `{}` | The first message sent by a client after connecting. It requests the server's media capabilities (supported codecs and RTP parameters). The server responds with the `router.rtpCapabilities` object. |
| `createWebRtcTransport` | Client → Server | `{ "direction": "send" }` or `{ "direction": "recv" }` | The client requests the creation of a transport for sending or receiving media. The server creates a `WebRtcTransport` and sends back its parameters (`id`, `iceParameters`, `dtlsParameters`). |
| `connectWebRtcTransport` | Client → Server | `{ "transportId": "...", "dtlsParameters": {...} }` | After the client creates its local transport, its `'connect'` event fires. The client sends the `dtlsParameters` from this event to the server, which is required to complete the secure DTLS handshake. |
| `produce` | Client → Server | `{ "transportId": "...", "kind": "audio", "rtpParameters": {...} }` | The client signals its intent to send a media stream. The server receives the RTP parameters and creates a corresponding server-side `Producer` on the specified transport. At this point, media begins flowing from the client to the server. |
| `newProducer` | Server → Client | `{ "producerId": "...", "participantId": "..." }` | A notification sent to all *other* participants in the session when a new producer becomes available. This informs clients that there is a new stream they can now choose to consume. |
| `consume` | Client → Server | `{ "producerId": "...", "rtpCapabilities": {...} }` | The client requests to receive a specific media stream. The server creates a server-side `Consumer` and sends its parameters back to the client, which then creates a local consumer to start receiving audio. |
| `producerClosed` | Server → Client | `{ "producerId": "..." }` | A notification sent when a producer is closed (e.g., a participant leaves the call). This allows clients to clean up the corresponding audio elements and remote tracks. |


## TODO
1. Implement RTP forking
2. Replace `console.log` for logging library such as [pino](https://github.com/pinojs/pino)
3. Use Redis for Session state instead of in-memory Map
4. Consider supporting JWT Session Refreshing
5. Metrics endpoint e.g. with prom-client
6. Containerise the application