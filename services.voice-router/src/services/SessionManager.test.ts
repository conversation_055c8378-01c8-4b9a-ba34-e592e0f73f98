import { SessionManager } from './SessionManager';
import { MediaServer } from '../core/MediaServer';
import { Session } from '../core/Session';

// Mock the dependencies
jest.mock('../core/MediaServer');
jest.mock('../core/Session');

describe('SessionManager', () => {
  let mediaServer: MediaServer;
  let sessionManager: SessionManager;

  beforeEach(() => {
    // Create fresh instances for each test
    mediaServer = new MediaServer();
    sessionManager = new SessionManager(mediaServer);
  });

  it('should create a new session successfully', async () => {
    const sessionId = await sessionManager.createSession();

    // Verify that the session ID is not null or undefined
    expect(sessionId).toBeDefined();

    // Verify that the session can be retrieved
    const session = sessionManager.getSession(sessionId);
    expect(session).toBeInstanceOf(Session);
  });

  it('should delete a session successfully', async () => {
    const sessionId = await sessionManager.createSession();
    const session = sessionManager.getSession(sessionId);

    // Ensure the session exists before deletion
    expect(session).toBeDefined();

    sessionManager.deleteSession(sessionId);

    // Verify that the session is no longer retrievable
    const deletedSession = sessionManager.getSession(sessionId);
    expect(deletedSession).toBeUndefined();
  });
});