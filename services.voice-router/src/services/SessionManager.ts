import { MediaServer } from '../core/MediaServer';
import { Session } from '../core/Session';

/**
 * Manages the lifecycle of all active media sessions (calls).
 */
export class SessionManager {
  private activeSessions = new Map<string, Session>();
  private mediaServer: MediaServer;

  constructor(mediaServer: MediaServer) {
    this.mediaServer = mediaServer;
  }

  /**
   * Creates a new media session and returns its ID.
   * @returns The unique ID of the newly created session.
   */
  public async createSession(): Promise<string> {
    const worker = this.mediaServer.getWorker();
    const session = new Session(worker);
    await session.start();

    this.activeSessions.set(session.id, session);
    console.log(` Created new session: ${session.id}`);

    return session.id;
  }

  /**
   * Retrieves an active session by its ID.
   * @param sessionId The ID of the session to retrieve.
   * @returns The Session instance, or undefined if not found.
   */
  public getSession(sessionId: string): Session | undefined {
    return this.activeSessions.get(sessionId);
  }

  /**
   * Deletes a session and cleans up its resources.
   * @param sessionId The ID of the session to delete.
   */
  public deleteSession(sessionId: string): void {
    const session = this.activeSessions.get(sessionId);
    if (session) {
      session.close();
      this.activeSessions.delete(sessionId);
      console.log(` Deleted session: ${sessionId}`);
    }
  }
}