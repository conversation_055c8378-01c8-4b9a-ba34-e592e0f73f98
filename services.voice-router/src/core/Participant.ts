import { WebSocket } from 'ws';
import { types as mediasoupTypes } from 'mediasoup';

/**
 * Represents a single participant in a session, managing their WebSocket
 * connection and all their associated MediaSoup resources.
 */
export class Participant {
  public readonly id: string;
  private ws: WebSocket;

  private transports = new Map<string, mediasoupTypes.WebRtcTransport>();
  private producers = new Map<string, mediasoupTypes.Producer>();
  private consumers = new Map<string, mediasoupTypes.Consumer>();

  constructor(id: string, ws: WebSocket) {
    this.id = id;
    this.ws = ws;
  }

  public addTransport(transport: mediasoupTypes.WebRtcTransport): void {
    this.transports.set(transport.id, transport);
  }

  public async connectTransport(transportId: string, dtlsParameters: mediasoupTypes.DtlsParameters): Promise<void> {
    const transport = this.transports.get(transportId);
    if (!transport) throw new Error(`Transport ${transportId} not found for participant ${this.id}`);
    await transport.connect({ dtlsParameters });
  }

  public async createProducer(options: {
    transportId: string;
    kind: mediasoupTypes.MediaKind;
    rtpParameters: mediasoupTypes.RtpParameters;
  }): Promise<mediasoupTypes.Producer> {
    const { transportId, kind, rtpParameters } = options;
    const transport = this.transports.get(transportId);
    if (!transport) throw new Error(`Transport ${transportId} not found for participant ${this.id}`);

    const producer = await transport.produce({ kind, rtpParameters });
    this.producers.set(producer.id, producer);

    producer.on('transportclose', () => {
      this.producers.delete(producer.id);
    });

    return producer;
  }

  public async createConsumer(options: {
    transportId: string;
    producerId: string;
    rtpCapabilities: mediasoupTypes.RtpCapabilities;
  }): Promise<mediasoupTypes.Consumer> {
    const { transportId, producerId, rtpCapabilities } = options;
    const transport = this.transports.get(transportId);
    if (!transport) throw new Error(`Transport ${transportId} not found for participant ${this.id}`);

    const consumer = await transport.consume({
      producerId,
      rtpCapabilities,
      paused: true, // Start paused and have the client resume once ready
    });
    this.consumers.set(consumer.id, consumer);

    consumer.on('transportclose', () => {
      this.consumers.delete(consumer.id);
    });

    return consumer;
  }

  /**
   * Sends a notification message (no response expected) to this participant.
   */
  public notify(method: string, data: any): void {
    this.ws.send(JSON.stringify({ type: 'notification', method, data }));
  }

  /**
   * Closes all resources associated with this participant.
   */
  public close(): void {
    this.transports.forEach((transport) => transport.close());
  }
}