import { types as mediasoupTypes } from 'mediasoup';
import config from '../config';
import { Participant } from './Participant';
import { WebSocket } from 'ws';

// Type definition for the transport information sent to the client
interface WebRtcTransportInfo {
  id: string;
  iceParameters: mediasoupTypes.IceParameters;
  iceCandidates: mediasoupTypes.IceCandidate;
  dtlsParameters: mediasoupTypes.DtlsParameters;
}

/**
 * Represents a single media session, encapsulating a MediaSoup Router
 * and managing all its participants.
 */
export class Session {
  public readonly id: string;
  private router: mediasoupTypes.Router;
  private worker: mediasoupTypes.Worker;

  private participants = new Map<string, Participant>();

  constructor(worker: mediasoupTypes.Worker) {
    this.worker = worker;
    this.router = null!; 
    this.id = '';
  }

  public async start(): Promise<void> {
    this.router = await this.worker.createRouter({
      mediaCodecs: config.mediasoup.router.mediaCodecs,
    });
    (this as { id: string }).id = this.router.id;
  }

  // --- Participant Management ---

  public addParticipant(participantId: string, ws: WebSocket): void {
    const participant = new Participant(participantId, ws);
    this.participants.set(participantId, participant);
  }

  public removeParticipant(participantId: string): void {
    const participant = this.participants.get(participantId);
    if (participant) {
      participant.close();
      this.participants.delete(participantId);
    }
  }

  // --- Media Logic ---

  public getRouterRtpCapabilities(): mediasoupTypes.RtpCapabilities {
    return this.router.rtpCapabilities;
  }

  public async createWebRtcTransport(options: { participantId: string }): Promise<WebRtcTransportInfo> {
    const participant = this.participants.get(options.participantId);
    if (!participant) throw new Error(`Participant ${options.participantId} not found`);

    const transport = await this.router.createWebRtcTransport(config.mediasoup.webRtcTransport);
    participant.addTransport(transport);

    return {
      id: transport.id,
      iceParameters: transport.iceParameters,
      iceCandidates: transport.iceCandidates,
      dtlsParameters: transport.dtlsParameters,
    };
  }

  public async connectWebRtcTransport(options: {
    participantId: string;
    transportId: string;
    dtlsParameters: mediasoupTypes.DtlsParameters;
  }): Promise<void> {
    const participant = this.participants.get(options.participantId);
    if (!participant) throw new Error(`Participant ${options.participantId} not found`);
    await participant.connectTransport(options.transportId, options.dtlsParameters);
  }

  public async createProducer(options: {
    participantId: string;
    transportId: string;
    kind: mediasoupTypes.MediaKind;
    rtpParameters: mediasoupTypes.RtpParameters;
  }): Promise<string> {
    const participant = this.participants.get(options.participantId);
    if (!participant) throw new Error(`Participant ${options.participantId} not found`);

    const producer = await participant.createProducer(options);

    // Notify all other participants that a new producer is available
    this.participants.forEach((otherParticipant) => {
      if (otherParticipant.id!== participant.id) {
        otherParticipant.notify('newProducer', {
          participantId: participant.id,
          producerId: producer.id,
        });
      }
    });

    return producer.id;
  }

  public async createConsumer(options: {
    participantId: string;
    transportId: string;
    producerId: string;
    rtpCapabilities: mediasoupTypes.RtpCapabilities;
  }) {
    const consumingParticipant = this.participants.get(options.participantId);
    if (!consumingParticipant) throw new Error(`Consuming participant ${options.participantId} not found`);

    // The server must check if the router can consume the producer
    if (!this.router.canConsume({ producerId: options.producerId, rtpCapabilities: options.rtpCapabilities })) {
      throw new Error(`Router cannot consume producer ${options.producerId}`);
    }

    const consumer = await consumingParticipant.createConsumer({
      transportId: options.transportId,
      producerId: options.producerId,
      rtpCapabilities: options.rtpCapabilities,
    });

    return {
      id: consumer.id,
      producerId: consumer.producerId,
      kind: consumer.kind,
      rtpParameters: consumer.rtpParameters,
    };
  }

  public close(): void {
    if (this.router) {
      this.router.close();
      this.router = null!;
      console.log(`Session ${this.id} closed.`);
    }
  }
}