import http from 'http';
import { WebSocketServer, WebSocket } from 'ws';
import { handleWebSocketMessage } from './handlers';
import { SessionManager } from '../../services/SessionManager';
import url from 'url';
import { v4 as uuidV4 } from 'uuid';

/**
 * Creates and attaches the WebSocket server to the main HTTP server.
 * @param httpServer - The http.Server instance.
 * @param sessionManager - The singleton instance of the SessionManager.
 */
export function createWebSocketServer(httpServer: http.Server, sessionManager: SessionManager) {
  const wss = new WebSocketServer({ server: httpServer });

  wss.on('connection', (ws: WebSocket, req: http.IncomingMessage) => {
    console.log('--> New WebSocket connection received.');

    const { query } = url.parse(req.url || '', true);
    const sessionId = query.sessionId as string;

    if (!sessionId) {
      console.error('WebSocket connection rejected: No sessionId provided.');
      ws.close(1008, 'No sessionId provided');
      return;
    }

    const session = sessionManager.getSession(sessionId);
    if (!session) {
      console.error(`WebSocket connection rejected: Session ${sessionId} not found.`);
      ws.close(1011, 'Session not found');
      return;
    }

    // Generate a unique ID for this participant
    const participantId = uuidV4();
    console.log(`WebSocket connection associated with session: ${sessionId}, participant: ${participantId}`);

    // Add the participant to the session
    session.addParticipant(participantId, ws);

    ws.on('message', async (message: string) => {
      try {
        const jsonMessage = JSON.parse(message);
        // Delegate message processing, now passing the participantId
        await handleWebSocketMessage(ws, jsonMessage, session, participantId);
      } catch (error) {
        console.error('Failed to parse WebSocket message or handle request:', error);
        ws.send(JSON.stringify({ error: 'Invalid message format' }));
      }
    });

    ws.on('close', () => {
      console.log(`WebSocket connection closed for participant: ${participantId}`);
      // Clean up resources when a participant disconnects
      session.removeParticipant(participantId);
    });

    ws.on('error', (error) => {
      console.error(`WebSocket error for participant ${participantId}:`, error);
    });
  });
}