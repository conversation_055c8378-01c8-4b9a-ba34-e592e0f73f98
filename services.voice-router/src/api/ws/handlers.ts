import { WebSocket } from 'ws';
import { Session } from '../../core/Session';
import { ConsumeRequest } from '../../types';

// A simple helper to structure responses
function sendResponse(ws: WebSocket, requestId: number, data: any) {
  ws.send(JSON.stringify({ type: 'response', id: requestId, data }));
}

function sendError(ws: WebSocket, requestId: number, error: string) {
  ws.send(JSON.stringify({ type: 'response', id: requestId, error }));
}

/**
 * Main handler for all incoming WebSocket messages.
 * @param ws - The WebSocket connection instance.
 * @param message - The parsed JSON message from the client.
 * @param session - The Session instance associated with this connection.
 * @param participantId - The unique ID of the participant sending the message.
 */
export async function handleWebSocketMessage(ws: WebSocket, message: any, session: Session, participantId: string) {
  const { id, method, data } = message;

  console.log(`Received WS message: method='${method}' from participant='${participantId}'`);

  try {
    switch (method) {
      case 'getRouterRtpCapabilities': {
        const capabilities = session.getRouterRtpCapabilities();
        sendResponse(ws, id, capabilities);
        break;
      }

      case 'createWebRtcTransport': {
        const transportInfo = await session.createWebRtcTransport({ participantId });
        sendResponse(ws, id, transportInfo);
        break;
      }

      case 'connectWebRtcTransport': {
        await session.connectWebRtcTransport({
          participantId,
          transportId: data.transportId,
          dtlsParameters: data.dtlsParameters,
        });
        sendResponse(ws, id, { connected: true });
        break;
      }

      case 'produce': {
        const { transportId, kind, rtpParameters } = data;
        const producerId = await session.createProducer({ participantId, transportId, kind, rtpParameters });
        sendResponse(ws, id, { id: producerId });
        break;
      }

      case 'consume': {
        const consumeData = data as ConsumeRequest;
        const consumerInfo = await session.createConsumer({ participantId, ...consumeData });
        sendResponse(ws, id, consumerInfo);
        break;
      }

      default: {
        console.warn(`Unknown WebSocket method: ${method}`);
        sendError(ws, id, `Unknown method: ${method}`);
        break;
      }
    }
  } catch (error: any) {
    console.error(`Error handling WebSocket method '${method}':`, error);
    sendError(ws, id, error.message || 'An internal server error occurred');
  }
}