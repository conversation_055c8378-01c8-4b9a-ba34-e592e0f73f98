import { types as mediasoupTypes } from 'mediasoup';

export const mediasoupConfig = {
  worker: {
    logLevel: 'warn' as mediasoupTypes.WorkerLogLevel,
    logTags: [
      'info',
      'ice',
      'dtls',
      'rtp',
      'srtp',
      'rtcp',
    ] as mediasoupTypes.WorkerLogTag,
    rtcMinPort: 40000,
    rtcMaxPort: 49999,
  },
  router: {
    mediaCodecs: [] as mediasoupTypes.RtpCodecCapability[],
  },
  webRtcTransport: {
    listenIps: [],
    enableUdp: true,
    enableTcp: true,
    preferUdp: true,
  },
};