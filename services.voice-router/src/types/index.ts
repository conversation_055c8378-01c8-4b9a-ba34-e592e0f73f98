import { SessionManager } from '../services/SessionManager';
import { types as mediasoupTypes } from 'mediasoup';

// Extend the Express Request object to hold our SessionManager instance.
declare global {
  namespace Express {
    export interface Request {
      sessionManager: SessionManager;
    }
  }
}

// Define a type for the body of a "start fork" request.
export interface StartForkBody {
  ip: string;
  port: number;
  // other potential options like codec, etc.
}

// Type for the data required to create a consumer on the server.
export interface ConsumeRequest {
  transportId: string;
  producerId: string;
  rtpCapabilities: mediasoupTypes.RtpCapabilities;
}