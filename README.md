# Cortexa

A real-time, AI-powered assistant for emergency call centers

## Overview

Cortexa's core mission is to reduce response times, improve accuracy in information gathering, and break down language barriers, ultimately leading to better outcomes for callers in distress. To achieve this, Cortexa augments the capabilities of human operators by providing instant transcription, speech-to-speech translation for non-native speakers, and intelligent assistance features. The system is built on a scalable microservice architecture to ensure high availability and low latency, which are critical in emergency response scenarios.

## Core Features

- **Real-Time Transcription:** Live, streaming transcription of dialogue from both the caller and the operator.

- **Speech-to-Speech Translation (S2ST):** Near real-time translation for calls where a language barrier exists. The operator can hear the caller's speech translated into their native language.

- **Call Management:** A complete system for queuing, assigning, and managing the lifecycle of incoming emergency calls.

## System Architecture

The system is designed as a set of decoupled microservices that communicate via APIs and an event bus. This separation of concerns allows for independent scaling, development, and maintenance of each component.

<img src="docs/diagrams/ARCHITECTURE.png" width="70%" alt="System Architecture">

| Service             | Description                                                | Responsibilities                                                                                                                                                 |
|---------------------|------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| API Gateway         | The single entry point for all external traffic.           | Routes incoming requests to the appropriate service, handles SSL termination, and manages API security (e.g., JWT validation).                                   |
| User Interface      | The web-based front-end application used by call center operators. | Provides the operator with call controls, displays real-time transcriptions, and renders AI-driven suggestions.                                                   |
| Call Control Service| The "brain" of the system. It manages the state and business logic of all calls. | - Exposes a REST API for call lifecycle management (create, assign, end).<br>- Orchestrates other services by sending commands (e.g., tells the SFU to start forking audio). |
| Voice Router (SFU)  | A Selective Forwarding Unit that manages all real-time audio streams. | - Establishes WebRTC connections with clients (users, AI services).<br>- Routes and forks RTP audio streams between participants and services.<br>- Exposes an internal API for control by the Call Control Service. |
| Transcription Service | A dedicated service for speech-to-text conversion.        | - Consumes RTP audio streams from the SFU.<br>- Performs real-time transcription.<br>- Publishes transcription events to the Kafka message bus.                   |
| Translation Service | A service for speech-to-speech translation.                | - Consumes RTP audio from the SFU.<br>- Translates the speech and generates a new audio stream.<br>- Publishes the translated audio back to the SFU as a new WebRTC track. |
| Kafka               | The central event bus for asynchronous communication.      | Decouples services by allowing them to publish and subscribe to system-wide events (e.g., call-started, transcription-received) without direct knowledge of each other. |




## Call Flow Walkthrough

A typical call follows this sequence of interactions:

1. **Initiation:** A VOIP System Integration receives an incoming phone call and makes an HTTP POST request to the Call Control Service to create a new call record. The call is placed in a queue.

2. **Assignment:** The User Interface receives a call-queued event via its WebSocket connection and displays the incoming call to an available operator. The operator clicks "Accept."

3. **Connection:**
    1. The User Interface sends an HTTP request to the Call Control Service to mark the call as active.
    2. It then opens a WebSocket connection to the Voice Router (SFU) to begin the WebRTC signaling handshake.

4. **Audio Transmission:** A secure WebRTC connection is established between the operator's browser and the SFU. Audio begins to flow.

5. **Transcription & AI Processing:**

    1. The Call Control Service sends a command to the SFU, instructing it to fork the call's audio stream and send it via RTP to the Transcription Service.

    2. The Transcription Service processes the audio and publishes transcription events to a Kafka topic.

    3. The User Interface consumes these events and displays the live transcript to the operator.

    4. Translation (If Activated): If the operator enables translation, the Call Control Service commands the SFU to also fork the audio to the Translation Service, which sends back a new, translated audio stream for the operator to listen to.

    5. Termination: When the call is finished, the operator clicks "End Call." The User Interface notifies the Call Control Service, which then orchestrates the teardown of all associated connections and resources.